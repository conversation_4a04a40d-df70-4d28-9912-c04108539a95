import React, { useState, useMemo } from 'react'
import { Trash2, CheckSquare, Square, Search, RefreshCw, User, Video, Calendar, Image } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { WithConfirm } from '@/components/WithConfirm'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useQuery } from '@tanstack/react-query'
import { useRequest } from '@/hooks/useRequest'
import { MatrixModule } from '@/libs/request/api/matrix'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { queryClient } from '@/main'
import { createSearchParams, useNavigate } from 'react-router'
import { CreateMatrixParams, PublishMode, Setting } from '@/types/matrix/douyin'
import { AuthedImgByObjectId } from '@/components/authed-img'

type DraftItem = CreateMatrixParams & { id: number }

export default function DistributionDraftPage() {
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [searchKeyword, setSearchKeyword] = useState('')
  const navigate = useNavigate()

  const draftQuery = useQuery({
    queryKey: [QUERY_KEYS.MATRIX_DRAFT_LIST],
    queryFn: () => MatrixModule.dyAccount.draftList()
  })

  const { mutate: deleteSingle } = useRequest(
    (id: number) => MatrixModule.dyAccount.deleteDraft(id),
    {
      actionName: '删除草稿',
      onSuccess: (_, deletedId) => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATRIX_DRAFT_LIST] })
        setSelectedIds(prev => prev.filter(selectedId => selectedId !== deletedId))
      }
    }
  )

  const { mutate: deleteBatch } = useRequest(
    (ids: number[]) => MatrixModule.dyAccount.batchDeleteDraft({ ids }),
    {
      actionName: '批量删除草稿',
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATRIX_DRAFT_LIST] })
        setSelectedIds([])
      }
    }
  )

  const allDrafts = useMemo(() => {
    const drafts = draftQuery.data || []

    if (searchKeyword.trim()) {
      return drafts.filter((draft: DraftItem) =>
        draft.name.toLowerCase().includes(searchKeyword.toLowerCase()) 
      )
    }

    return drafts
  }, [draftQuery.data, searchKeyword])

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedIds.length === allDrafts.length) {
      setSelectedIds([])
    } else {
      setSelectedIds(allDrafts.map(draft => draft.id))
    }
  }

  // 单个选择
  const handleSelectItem = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id])
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id))
    }
  }

  // 渲染草稿卡片
  const renderDraftCard = (draft: DraftItem) => {
    const isSelected = selectedIds.includes(draft.id)

    return (

      <div
        key={draft.id}
        className={`relative transition-all bg-background/70 border border-border rounded-lg duration-200 hover:border-neutral-600 ${
          isSelected ? 'ring-1 ring-gray-500 bg-primary/5' : ''
        }`}
      >

        {/* 选择框 */}
        <div className="absolute top-3 left-3 z-10">
          <Checkbox
            id={`selector-${draft.id}`}
            checked={isSelected}
            onCheckedChange={checked => handleSelectItem(draft.id, checked as boolean)}
            className="bg-white/90 backdrop-blur-sm"

          />
        </div>

        <div className="relative p-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0 ml-8">
              <h3 className="font-semibold text-base truncate" title={draft.name}>
                {draft.name}
              </h3>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-1 ml-2">
               
              <WithConfirm
                title="删除草稿"
                description={`确定要删除草稿"${draft.name}"吗？此操作不可恢复。`}
                confirmText="删除"
                confirmVariant="destructive"
                onConfirm={() => deleteSingle(draft.id)}
                asChild
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-destructive hover:text-destructive"

                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </WithConfirm>
            </div>
          </div>
        </div>

        <div className=" pt-0 p-3 cursor-pointer"
          onClick={() => {
            navigate({
              pathname: '/home/<USER>/distribution',
              search: createSearchParams({
                draftId: draft.id.toString()
              }).toString()
            })
          }}
        >
          <div className="flex items-start gap-3 h-full overflow-y-auto">
            {/* 视频预览 */}
            {draft.videoList && draft.videoList.length > 0 && draft.videoList[0].cover ? (
              <div className="relative aspect-[9/16] w-32 bg-muted rounded-lg overflow-hidden">
                <AuthedImgByObjectId
                  src={draft.videoList[0].cover}
                  alt="视频封面"
                  className="w-full h-full object-cover"
                  onError={e => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
                {
                  draft.videoList.length > 1 && (
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      +{draft.videoList.length - 1} 个视频
                    </div>
                  )
                }
              </div>
            ) : (
              <div className="flex justify-center items-center aspect-[9/16] w-32 bg-accent/60 rounded-lg">
                <Image  />
              </div>
            )}

            <div className="flex flex-col flex-1 gap-4">
              {/* 统计信息 */}
              <div className="flex gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">账号:</span>
                  <span className="font-medium">{draft.totalAccount}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Video className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">视频:</span>
                  <span className="font-medium">{draft.totalVideo}</span>
                </div>
              </div>

              {/* 发布设置 */}
              <div className="text-sm">
                <span className="text-muted-foreground">发布设置:</span>
                <span className="ml-2 font-medium">
                  {draft.setting === Setting.ONE_ACCOUNT_ONE_VIDEO
                    ? '一个账号一个视频'
                    : '一个账号多个视频'}
                </span>
              </div>

              {/* 发布模式 */}
              <div className="text-sm">
                <span className="text-muted-foreground">发布模式:</span>
                <span className="ml-2 font-medium">
                  {draft.publishMode === PublishMode.CHECK_IN
                    ? '打卡'
                    : '带货'}
                </span>
              </div>

              {/* 草稿ID */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>草稿 ID: {draft.id}</span>
              </div>
            </div>
          </div>
        </div>

      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-background/50 rounded-lg overflow-y-auto">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between p-6 border-b bg-background/50 rounded-t-lg backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold text-gradient-brand">草稿箱</h2>

          <div className="text-sm text-muted-foreground">
            共 {allDrafts.length} 个草稿
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索草稿名称..."
              value={searchKeyword}
              onChange={e => setSearchKeyword(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          {/* 刷新按钮 */}
          <Button
            variant="outline"
            onClick={() => draftQuery.refetch()}
            disabled={draftQuery.isFetching}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${draftQuery.isFetching ? 'animate-spin' : ''}`} />
            刷新
          </Button>

          {/* 全选按钮 */}
          <Button
            variant="outline"
            onClick={handleSelectAll}
            className="flex items-center gap-2"
          >
            {selectedIds.length === allDrafts.length && allDrafts.length > 0 ? (
              <CheckSquare className="h-4 w-4" />
            ) : (
              <Square className="h-4 w-4" />
            )}
            {selectedIds.length === allDrafts.length && allDrafts.length > 0 ? '取消全选' : '全选'}
          </Button>

          {/* 批量删除按钮 */}
          {selectedIds.length > 0 && (
            <WithConfirm
              title="批量删除草稿"
              description={`确定要删除选中的 ${selectedIds.length} 个草稿吗？此操作不可恢复。`}
              confirmText="删除"
              confirmVariant="destructive"
              onConfirm={() => deleteBatch(selectedIds)}
              asChild
            >
              <Button
                variant="destructive"
                size="sm"
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                删除选中 ({selectedIds.length})
              </Button>
            </WithConfirm>
          )}
        </div>
      </div>

      {/* 草稿列表 */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          {draftQuery.isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-muted-foreground">加载草稿中...</div>
            </div>
          ) : draftQuery.isError ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-destructive">加载失败，请重试</div>
            </div>
          ) : allDrafts.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-muted-foreground">
                {searchKeyword ? `没有找到包含"${searchKeyword}"的草稿` : '暂无草稿，去发布页面创建一个吧！'}
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6">
              {allDrafts.map(draft => renderDraftCard(draft))}
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  )
}
