import { ICacheManager, SubCacheManager } from '../types'
import { Mixcut } from '@/types/mixcut'

export class MixcutCacheManager extends SubCacheManager {

  constructor(parent: ICacheManager) {
    super(parent, 'mixcut_cache', '混剪缓存')
  }

  async cacheMixcut(...mixcuts: Mixcut.SavedMixcut[]): Promise<void> {

  }

  async getMixcut(id: number): Promise<Mixcut.SavedMixcut | null> {
    return await this.store.getItem(id.toString())
  }

  async cleanup(now: number, maxAge: number) {
    const mixcutKeysToRemove: string[] = []
    await this.store.iterate((mixcut: Mixcut.SavedMixcut, key: string) => {
      if (now - mixcut.createAt > maxAge) {
        mixcutKeysToRemove.push(key)
      }
    })

    for (const key of mixcutKeysToRemove) {
      await this.store.removeItem(key)
    }
  }
}
