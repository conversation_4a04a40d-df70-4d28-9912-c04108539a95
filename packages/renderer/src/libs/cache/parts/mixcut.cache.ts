import { ICacheManager, SubCacheManager } from '../types'
import { Mixcut } from '@/types/mixcut'

export class MixcutCacheManager extends SubCacheManager {

  constructor(parent: ICacheManager) {
    super(parent, 'mixcut_cache', '混剪缓存')
  }

  async cacheMixcut(...mixcuts: Mixcut.SavedMixcut[]): Promise<void> {
    console.log(`[MixcutCacheManager] 开始缓存 ${mixcuts.length} 个混剪数据`)

    // 过滤出需要缓存的混剪（未缓存的）
    const mixcutsToCache: Array<{ id: number; url: string }> = []

    for (const mixcut of mixcuts) {
      const { id, url } = mixcut

      if (!url) {
        console.warn(`[MixcutCacheManager] 混剪 ${id} 缺少 URL，跳过缓存`)
        continue
      }

      // 检查是否已经缓存
      const existingCache = await this.store.getItem(id.toString())
      if (existingCache) {
        console.log(`[MixcutCacheManager] 混剪 ${id} 已缓存，跳过`)
        continue
      }

      mixcutsToCache.push({ id, url })
    }

    if (mixcutsToCache.length === 0) {
      console.log('[MixcutCacheManager] 没有需要缓存的混剪数据')
      return
    }

    try {
      // 调用主进程的 IPC 接口进行缓存
      const result = await window.mixcut.cacheMixcutData({ mixcuts: mixcutsToCache })

      console.log('[MixcutCacheManager] 主进程缓存结果:', result)

      // 将成功缓存的数据存储到 IndexedDB
      for (const cacheItem of result.cached) {
        const originalMixcut = mixcuts.find(m => m.id === cacheItem.id)
        if (originalMixcut) {
          await this.store.setItem(cacheItem.id.toString(), {
            ...originalMixcut,
            encryptedData: cacheItem.encryptedData,
            cachedAt: cacheItem.cachedAt,
            originalUrl: cacheItem.originalUrl
          })
        }
      }

      // 记录错误
      if (result.errors.length > 0) {
        console.error('[MixcutCacheManager] 部分混剪缓存失败:', result.errors)
      }

      console.log(`[MixcutCacheManager] 混剪数据缓存完成，成功: ${result.cached.length}, 失败: ${result.errors.length}`)
    } catch (error) {
      console.error('[MixcutCacheManager] 调用主进程缓存接口失败:', error)
      throw error
    }
  }

  async getMixcut(id: number): Promise<Mixcut.SavedMixcut | null> {
    return await this.store.getItem(id.toString())
  }

  /**
   * 获取解密后的混剪 JSON 数据
   * @param id 混剪 ID
   * @returns 解密后的 JSON 数据，如果未缓存则返回 null
   */
  async getMixcutData(id: number): Promise<any | null> {
    try {
      const cachedMixcut = await this.store.getItem<Mixcut.SavedMixcut & { encryptedData?: string }>(id.toString())

      if (!cachedMixcut?.encryptedData) {
        return null
      }

      // 调用主进程的解密接口
      const decryptedData = await window.mixcut.decryptMixcutData({
        id,
        encryptedData: cachedMixcut.encryptedData
      })

      return decryptedData
    } catch (error) {
      console.error(`[MixcutCacheManager] 获取混剪数据失败 (ID: ${id}):`, error)
      return null
    }
  }

  /**
   * 检查混剪是否已缓存
   * @param id 混剪 ID
   * @returns 是否已缓存
   */
  async isMixcutCached(id: number): Promise<boolean> {
    const cachedMixcut = await this.store.getItem(id.toString())
    return cachedMixcut !== null
  }

  async cleanup(now: number, maxAge: number) {
    const mixcutKeysToRemove: string[] = []
    await this.store.iterate((mixcut: Mixcut.SavedMixcut, key: string) => {
      if (now - mixcut.createAt > maxAge) {
        mixcutKeysToRemove.push(key)
      }
    })

    for (const key of mixcutKeysToRemove) {
      await this.store.removeItem(key)
    }
  }
}
