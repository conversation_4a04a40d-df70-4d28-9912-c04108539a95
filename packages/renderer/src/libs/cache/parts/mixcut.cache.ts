import { ICacheManager, SubCacheManager } from '../types'
import { Mixcut } from '@/types/mixcut'
import { parseUrlFromObjectHref } from '@/libs/tools/resource'
import { MixcutEncryption } from '../utils/encryption'

export class MixcutCacheManager extends SubCacheManager {

  constructor(parent: ICacheManager) {
    super(parent, 'mixcut_cache', '混剪缓存')
  }

  async cacheMixcut(...mixcuts: Mixcut.SavedMixcut[]): Promise<void> {
    console.log(`[MixcutCacheManager] 开始缓存 ${mixcuts.length} 个混剪数据`)

    for (const mixcut of mixcuts) {
      try {
        // 提取 id 和 url 字段
        const { id, url } = mixcut

        if (!url) {
          console.warn(`[MixcutCacheManager] 混剪 ${id} 缺少 URL，跳过缓存`)
          continue
        }

        // 检查是否已经缓存
        const existingCache = await this.store.getItem(id.toString())
        if (existingCache) {
          console.log(`[MixcutCacheManager] 混剪 ${id} 已缓存，跳过`)
          continue
        }

        // 解析 Object Href URL 为实际下载链接
        const downloadUrl = await parseUrlFromObjectHref(url)

        // 直接下载 JSON 内容
        const response = await fetch(downloadUrl)
        if (!response.ok) {
          throw new Error(`下载失败: ${response.status} ${response.statusText}`)
        }

        const jsonData = await response.json()

        // 加密 JSON 数据
        const encryptedData = MixcutEncryption.compressAndEncrypt(jsonData)

        console.log(`[MixcutCacheManager] 混剪 ${id} 下载并加密成功`)

        // 将混剪元数据和加密数据存储到本地 IndexedDB
        await this.store.setItem(id.toString(), {
          ...mixcut,
          encryptedData, // 加密后的 JSON 数据
          cachedAt: Date.now(), // 添加缓存时间戳
          originalUrl: downloadUrl // 保存原始下载链接
        })
      } catch (error) {
        console.error(`[MixcutCacheManager] 缓存混剪 ${mixcut.id} 失败:`, error)
        // 继续处理下一个，不中断整个缓存过程
      }
    }

    console.log('[MixcutCacheManager] 混剪数据缓存完成')
  }

  async getMixcut(id: number): Promise<Mixcut.SavedMixcut | null> {
    return await this.store.getItem(id.toString())
  }

  /**
   * 获取解密后的混剪 JSON 数据
   * @param id 混剪 ID
   * @returns 解密后的 JSON 数据，如果未缓存则返回 null
   */
  async getMixcutData(id: number): Promise<any | null> {
    try {
      const cachedMixcut = await this.store.getItem<Mixcut.SavedMixcut & { encryptedData?: string }>(id.toString())

      if (!cachedMixcut?.encryptedData) {
        return null
      }

      // 解密数据
      const decryptedData = MixcutEncryption.decryptAndDecompress(cachedMixcut.encryptedData)
      return decryptedData
    } catch (error) {
      console.error(`[MixcutCacheManager] 获取混剪数据失败 (ID: ${id}):`, error)
      return null
    }
  }

  /**
   * 检查混剪是否已缓存
   * @param id 混剪 ID
   * @returns 是否已缓存
   */
  async isMixcutCached(id: number): Promise<boolean> {
    const cachedMixcut = await this.store.getItem(id.toString())
    return cachedMixcut !== null
  }

  async cleanup(now: number, maxAge: number) {
    const mixcutKeysToRemove: string[] = []
    await this.store.iterate((mixcut: Mixcut.SavedMixcut, key: string) => {
      if (now - mixcut.createAt > maxAge) {
        mixcutKeysToRemove.push(key)
      }
    })

    for (const key of mixcutKeysToRemove) {
      await this.store.removeItem(key)
    }
  }
}
