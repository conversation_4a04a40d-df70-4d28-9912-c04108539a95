import CryptoJS from 'crypto-js'

/**
 * 混剪数据加密工具
 * 使用 AES 加密算法对 JSON 数据进行加密和解密
 */
export class MixcutEncryption {

  // 硬编码的加密密钥（根据需求暂时采用明文存储）
  private static readonly ENCRYPTION_KEY = 'mixcut-cache-key-2024'

  /**
   * 加密 JSON 数据
   * @param data 要加密的数据对象
   * @returns 加密后的字符串
   */
  static encrypt(data: any): string {
    try {
      const jsonString = JSON.stringify(data)
      const encrypted = CryptoJS.AES.encrypt(jsonString, this.ENCRYPTION_KEY).toString()
      return encrypted
    } catch (error) {
      console.error('[MixcutEncryption] 加密失败:', error)
      throw new Error('数据加密失败')
    }
  }

  /**
   * 解密数据
   * @param encryptedData 加密的字符串
   * @returns 解密后的数据对象
   */
  static decrypt<T = any>(encryptedData: string): T {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY)
      const jsonString = decrypted.toString(CryptoJS.enc.Utf8)

      if (!jsonString) {
        throw new Error('解密结果为空，可能密钥不正确')
      }

      return JSON.parse(jsonString)
    } catch (error) {
      console.error('[MixcutEncryption] 解密失败:', error)
      throw new Error('数据解密失败')
    }
  }

  /**
   * 压缩并加密数据（使用 LZ-string 进行压缩以减少体积）
   * @param data 要加密的数据对象
   * @returns 压缩加密后的字符串
   */
  static compressAndEncrypt(data: any): string {
    try {
      const jsonString = JSON.stringify(data)
      // 使用 CryptoJS 内置的压缩功能
      const compressed = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(jsonString))
      const encrypted = CryptoJS.AES.encrypt(compressed, this.ENCRYPTION_KEY).toString()
      return encrypted
    } catch (error) {
      console.error('[MixcutEncryption] 压缩加密失败:', error)
      throw new Error('数据压缩加密失败')
    }
  }

  /**
   * 解密并解压数据
   * @param encryptedData 压缩加密的字符串
   * @returns 解密解压后的数据对象
   */
  static decryptAndDecompress<T = any>(encryptedData: string): T {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY)
      const compressed = decrypted.toString(CryptoJS.enc.Utf8)

      if (!compressed) {
        throw new Error('解密结果为空，可能密钥不正确')
      }

      const jsonString = CryptoJS.enc.Base64.parse(compressed).toString(CryptoJS.enc.Utf8)
      return JSON.parse(jsonString)
    } catch (error) {
      console.error('[MixcutEncryption] 解密解压失败:', error)
      throw new Error('数据解密解压失败')
    }
  }
}
