import { Track, TrackType } from '@/modules/video-editor/types'
import { Overlay, OverlayType, StoryboardOverlay } from '@clipnest/remotion-shared/types'
import {
  DraggableState,
  OverlayDragInfo,
  OverlaysAdjustment,
  useEditorContext,
  useTimelineContext,
  useTimelineDnd
} from '@/modules/video-editor/contexts'
import {
  byStartFrame,
  byStoryboard,
  findOverlaysAboveStorybook,
  findOverlaysBetweenFrames,
  findTrackByOverlay,
  forRelevantOverlays,
  getOverlayTimeRange,
  isValidProgressiveOverlay
} from '@/modules/video-editor/utils/overlay-helper'
import { calculateLeftSpaceForResizing, findStoryboardByFromFrame } from '@/modules/video-editor/utils/track-helper'
import { useCallback } from 'react'
import { snapToGrid } from '@/modules/video-editor/contexts/timeline-dnd/utils'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'

function calcMaxDuration(overlay: Overlay) {
  if (isValidProgressiveOverlay(overlay)) {
    return (overlay.originalDurationFrames - (overlay.trimStartFrames ?? 0)) / (overlay.speed ?? 1)
  }
  return null
}

function calcAdjustForResize(
  tracks: Track[],
  currentTrack: Track,
  currentOverlay: Overlay,
  intendedNewDuration: number,
): Pick<Required<DraggableState>, 'adjustedDuration' | 'overlaysAdjust'> {
  const storyboard = currentTrack.isGlobalTrack
    ? null
    : findStoryboardByFromFrame(tracks, currentOverlay.from)

  const restOverlays = currentTrack
    .overlays
    .filter(forRelevantOverlays({
      currentOverlay,
      track: currentTrack,
      excludingOverlay: currentOverlay,
    }))
    .sort(byStartFrame())

  let adjustedDuration = intendedNewDuration

  // 当调整的是分镜时, 需要保证分镜的最短时长
  if (currentOverlay.type === OverlayType.STORYBOARD) {
    const children = findOverlaysAboveStorybook(tracks, currentOverlay as StoryboardOverlay)
    const lastEndFrameOfChildren = children.reduce((max, o) => {
      const [, end] = getOverlayTimeRange(o)
      return Math.max(max, end)
    }, currentOverlay.from)

    adjustedDuration = Math.max(adjustedDuration, lastEndFrameOfChildren - currentOverlay.from)
  }
  // 当调整分镜下元素的时长时, 需要考虑分镜内的剩余空间, 并限制时长
  else if (storyboard) {
    const leftSpace = calculateLeftSpaceForResizing(currentTrack, storyboard, currentOverlay)

    adjustedDuration = Math.min(
      currentOverlay.durationInFrames + leftSpace,
      intendedNewDuration
    )
  }

  const maxDuration = calcMaxDuration(currentOverlay)
  if (maxDuration !== null) {
    adjustedDuration = Math.min(adjustedDuration, maxDuration)
  }

  const overlaysAdjust: OverlaysAdjustment = new OverlaysAdjustment()

  const [, currentOverlayEndFrame] = getOverlayTimeRange(currentOverlay)

  const intendedNewEnd = adjustedDuration + currentOverlay.from

  const trailingOverlays = findOverlaysBetweenFrames(
    restOverlays,
    currentOverlayEndFrame - 1,
    Infinity,
    'start'
  )

  if (!trailingOverlays.length) {
    return { overlaysAdjust, adjustedDuration }
  }

  // 移动当前 Overlay 后方的所有 Overlay, 以补齐空位或避免重叠
  // 如果当前 Overlay 是分镜 Overlay, 则该分镜下的所有 Overlay 也需要同步移动
  const relevantOverlays = currentOverlay.type === OverlayType.STORYBOARD
    ? trailingOverlays
      .filter((o): o is StoryboardOverlay & { index: number } => o.type === OverlayType.STORYBOARD)
      .map(storyboard => {
        return [storyboard, ...findOverlaysAboveStorybook(tracks, storyboard)]
      })
      .flat() as Overlay[]
    : trailingOverlays.filter(byStoryboard(storyboard))

  const durationShift = intendedNewEnd - trailingOverlays[0].from

  const shouldFillGap = storyboard !== null && (currentTrack.type === TrackType.VIDEO || currentTrack.type === TrackType.STORYBOARD)
  if (durationShift > 0 || shouldFillGap) {
    relevantOverlays.forEach(storyboard => {
      overlaysAdjust.set(storyboard.id, { fromFrameShift: durationShift })
    })
  }

  return { overlaysAdjust, adjustedDuration }
}

function calculateDraggableState(
  tracks: Track[],
  currentOverlay: Overlay,
  intendedNewDuration: number,
): DraggableState {
  if (!currentOverlay) {
    return {
      draggable: false,
    }
  }

  const track = findTrackByOverlay(tracks, currentOverlay.id) || null

  if (!track) {
    return {
      draggable: false
    }
  }

  return {
    draggable: true,
    adjustedStartFrame: currentOverlay.from,
    ...calcAdjustForResize(
      tracks,
      track,
      currentOverlay,
      intendedNewDuration,
    )
  }
}

/**
 * 处理 TimelineItem 调整时长
 */
export const useTimelineItemResizing = () => {
  const { zoomScale } = useTimelineContext()
  const { tracks } = useEditorContext()
  const { dragInfoRef, updateDraggableState } = useTimelineDnd()

  const calculateTargetDuration = useCallback((
    dragInfo: OverlayDragInfo,
    deltaX: number,
  ) => {
    const deltaFrame = snapToGrid(deltaX / zoomScale / PIXELS_PER_FRAME)

    return Math.max(1, dragInfo.initialDurationInFrames + deltaFrame)
  }, [zoomScale])

  const handleResizeMove = useCallback(
    (deltaX: number) => {
      if (!dragInfoRef.current) return

      const targetDuration = calculateTargetDuration(
        dragInfoRef.current,
        deltaX,
      )

      dragInfoRef.current.currentDuration = targetDuration

      updateDraggableState(
        calculateDraggableState(
          tracks,
          dragInfoRef.current.overlay,
          targetDuration,
        )
      )
    },
    [tracks, calculateTargetDuration],
  )

  return {
    handleResizeMove,
  }
}
