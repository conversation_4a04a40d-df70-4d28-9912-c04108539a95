import React, { PropsWith<PERSON>hildren, useCallback, useRef, useState } from 'react'
import { Overlay, Progressive } from '@clipnest/remotion-shared/types'
import { GhostElement } from '@/modules/video-editor/types'
import { TimelineDndContext, TimelineDndContextValues } from './context'
import { SingleOverlayUpdatePayload } from '@/modules/video-editor/utils/track-helper'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { getOverlayTrackIndex, isValidProgressiveOverlay } from '@/modules/video-editor/utils/overlay-helper'
import _ from 'lodash'
import { buildOverlayUpdates } from './utils'
import { useTimelineSnapping } from './useTimelineSnapping'
import { DraggableState, OverlayDragInfo, OverlaysAdjustment } from './types'

function buildUpdateFromDraggableState(overlay: Overlay, trackIndex: number, state: DraggableState) {
  const { adjustedDuration, adjustedStartFrame, targetStoryboardIndex } = state

  const update: SingleOverlayUpdatePayload = {
    ...overlay,
    targetTrackIndex: trackIndex
  }

  if (adjustedStartFrame !== undefined) {
    update.from = adjustedStartFrame
  }

  if (targetStoryboardIndex !== undefined) {
    update.storyboardIndex = targetStoryboardIndex
  }

  if (isValidProgressiveOverlay(overlay) && adjustedDuration) {
    const { speed = 1, originalDurationFrames, trimStartFrames = 0 } = overlay
    ;(update as Progressive).trimEndFrames = originalDurationFrames - trimStartFrames - adjustedDuration * speed
    update.durationInFrames = adjustedDuration
  } else if (adjustedDuration !== undefined) {
    update.durationInFrames = adjustedDuration
  }

  return update
}

export const TimelineDndProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { tracks, bulkUpdateOverlays } = useEditorContext()

  const [isDragging, setIsDragging] = useState(false)
  const [landingPoint, setLandingPoint] = useState<GhostElement | null>(null)
  const [draggingOverlay, setDraggingOverlay] = useState<Overlay | null>(null)
  const [mousePosition, setMousePosition] = useState<GhostElement | null>(null)
  const [previewOverlaysAdjust, setPreviewOverlaysAdjust] = useState<OverlaysAdjustment>(new OverlaysAdjustment())

  const dragInfoRef = useRef<OverlayDragInfo | null>(null)

  const resetDragState = () => {
    setIsDragging(false)
    setDraggingOverlay(null)
    setLandingPoint(null)
    setMousePosition(null)
    setPreviewOverlaysAdjust(new OverlaysAdjustment())
    dragInfoRef.current = null
  }

  const handleTimelineItemAdjustStart = useCallback(
    (overlay: Overlay) => {
      const row = getOverlayTrackIndex(tracks, overlay.id) || 0

      setIsDragging(true)
      setDraggingOverlay(overlay)

      dragInfoRef.current = {
        overlay,
        initialFrom: overlay.from,
        initialDurationInFrames: overlay.durationInFrames,
        initialRow: row,
      }

      setMousePosition({
        from: overlay.from,
        durationInFrames: overlay.durationInFrames,
        row,
        overlay,
      })
      setPreviewOverlaysAdjust(new OverlaysAdjustment())
    },
    [tracks]
  )

  const updateDraggableState  = (newState: DraggableState) => {
    const dragInfo = dragInfoRef.current
    if (!dragInfo) {
      return
    }

    dragInfo.draggableState = newState
    const {
      currentRow = dragInfo.initialRow,
      currentFrom = dragInfo.initialFrom,
      currentDuration = dragInfo.initialDurationInFrames,
    } = dragInfo

    const {
      draggable,
      adjustedRow = currentRow,
      adjustedStartFrame = currentFrom,
      adjustedDuration = currentDuration,
      overlaysAdjust = new OverlaysAdjustment()
    } = newState

    const mousePosition: GhostElement = {
      overlay: dragInfo.overlay,
      invalid: !draggable,
      row: currentRow,
      from: currentFrom,
      durationInFrames: currentDuration,
    }

    setMousePosition(mousePosition)

    if (draggable) {
      const newLandingPoint: GhostElement = _.merge({}, mousePosition, {
        from: adjustedStartFrame,
        durationInFrames: adjustedDuration,
        row: adjustedRow,
      })
      setLandingPoint(newLandingPoint)
      dragInfo.landingPoint = newLandingPoint
    } else {
      setLandingPoint(null)
    }

    setPreviewOverlaysAdjust(overlaysAdjust)
  }

  const handleTimelineItemAdjustEnd = useCallback(
    () => {
      const dragInfo = dragInfoRef.current

      if (
        !dragInfo
        || !dragInfo.landingPoint
        || !dragInfo.draggableState?.draggable
      ) {
        return resetDragState()
      }

      const { overlay: originalOverlay } = dragInfo

      if (!originalOverlay) {
        return resetDragState()
      }

      const { overlaysAdjust, draggable } = dragInfo.draggableState

      if (!draggable) {
        return resetDragState()
      }

      const predicatedRow = dragInfo.currentRow ?? dragInfo.initialRow

      const itemsToUpdate: SingleOverlayUpdatePayload[] = [
        buildUpdateFromDraggableState(originalOverlay, predicatedRow, dragInfo.draggableState),
        ...buildOverlayUpdates(tracks, overlaysAdjust)
      ]

      bulkUpdateOverlays(itemsToUpdate)
      resetDragState()
    },
    [tracks, dragInfoRef, bulkUpdateOverlays, resetDragState]
  )

  const { snappedLandingPoint, alignmentLines } = useTimelineSnapping({
    isDragging,
    draggingOverlay,
    landingPoint,
    dragInfoRef,
  })

  return (
    <TimelineDndContext.Provider
      value={{
        isDragging,
        draggingOverlay,
        mousePosition,
        landingPoint: snappedLandingPoint,
        previewOverlaysAdjust,
        dragInfoRef,
        alignmentLines,

        setIsDragging,
        setMousePosition,
        setLandingPoint,
        setPreviewOverlaysAdjust,
        resetDragState,
        updateDraggableState,

        handleTimelineItemAdjustStart,
        handleTimelineItemAdjustEnd,
      } as TimelineDndContextValues}
    >
      {children}
    </TimelineDndContext.Provider>
  )
}
