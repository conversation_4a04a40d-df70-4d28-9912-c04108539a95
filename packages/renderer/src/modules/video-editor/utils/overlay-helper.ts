import {
  Overlay,
  OverlayType,
  Progressive,
  RenderableOverlay,
  StoryboardOverlay,
  TextOverlay
} from '@clipnest/remotion-shared/types'
import { clamp, merge, omit } from 'lodash'
import { cacheManager } from '@/libs/cache/cache-manager'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'
import { IndexableOverlay, IndexableTrack, Track, TrackType } from '@/modules/video-editor/types'
import { flatOverlaysFromTracks, getStoryboards } from '@/modules/video-editor/utils/track-helper'
import opentype from 'opentype.js'
import { calculateTextRenderInfo } from '@clipnest/overlay-renderer'
import { DEFAULT_TEXT_OVERLAY } from '@/modules/video-editor/constants'

export function byStartFrame(order?: 'asc' | 'desc') {
  return (a: Overlay, b: Overlay) => {
    return (a.from - b.from) * (order === 'desc' ? -1 : 1)
  }
}

export function byStoryboard(storyboard?: IndexableOverlay | null) {
  return (o: Overlay) => {
    return !storyboard || storyboard.index === o.storyboardIndex
  }
}

export function forRelevantOverlays(filters: {
  storyboard?: IndexableOverlay | null,
  track?: Track | null,
  currentOverlay?: Pick<Overlay, 'type'> | null
  excludingOverlay?: Pick<Overlay, 'id'> | null
}) {
  const { storyboard, currentOverlay, excludingOverlay, track } = filters
  return (o: Overlay) => {
    let result: boolean = byStoryboard(storyboard)(o)

    if (track && track.type === TrackType.NARRATION && currentOverlay) {
      result &&= (o.type === currentOverlay.type)
    }

    if (excludingOverlay) {
      result &&= o.id !== excludingOverlay.id
    }

    return result
  }
}

/**
 * 获取 Overlay 的起始帧和终止帧
 */
export function getOverlayTimeRange(overlay?: Pick<Overlay, 'from' | 'durationInFrames'>) {
  if (!overlay) return [0, 0]

  return [overlay.from, overlay.from + overlay.durationInFrames]
}

/**
 * 检查两个 Overlay 是否重叠
 */
export function isTwoOverlaysOverlap(
  a: Pick<Overlay, 'from' | 'durationInFrames'>,
  b: Pick<Overlay, 'from' | 'durationInFrames'>
): boolean {
  const [aFrom, aEnd] = getOverlayTimeRange(a)
  const [bFrom, bEnd] = getOverlayTimeRange(b)
  return !(aEnd <= bFrom || aFrom >= bEnd)
}

/**
 * 根据 Overlay ID 查找所在的 Track
 */
export function findTrackByOverlay(tracks: Track[], id: number): IndexableTrack | undefined {
  const index = tracks.findIndex(({ overlays }) => overlays.some(o => o.id === id))
  if (index === -1) {
    return undefined
  }

  return {
    ...tracks[index],
    index
  }
}

/**
 * 根据 ID 查找指定的 Overlay
 */
export function findOverlay(trackOrTracks: Track[] | Track, id: number): Overlay | undefined {
  if (Array.isArray(trackOrTracks)) {
    return flatOverlaysFromTracks(trackOrTracks)
      .find(o => o.id === id)
  }

  return trackOrTracks.overlays.find(o => o.id === id)
}

/**
 * 查找指定的一组 Overlay 中最后的一个 Overlay (即 `from` 最大者)
 */
export function findLastOverlay(overlays: Overlay[]) {
  return overlays.sort(byStartFrame('desc')).at(0)
}

/**
 * 查询目标 Overlay 位于第几个 Track 中 (即第几行)
 */
export function getOverlayTrackIndex(tracks: Track[], id: number): number {
  return tracks.findIndex(track => track.overlays.some(o => o.id === id))
}

/**
 * 计算分镜内的剩余空间
 * @param storyboard 需要检查的分镜
 * @param overlays 当前已存在的 Overlays
 * @deprecated
 */
export function calculateLeftSpaceOfStoryboard(storyboard: Overlay, overlays: Overlay[]): number {
  const [storyboardFrom, storyboardEnd] = getOverlayTimeRange(storyboard)
  let lastStartFrame = storyboardFrom

  for (const overlay of overlays) {
    if (isTwoOverlaysOverlap(overlay, storyboard)) {
      const [, end] = getOverlayTimeRange(overlay)
      lastStartFrame = Math.max(end, lastStartFrame)
    }
  }

  return Math.max(0, storyboardEnd - lastStartFrame)
}

/**
 * 查询目标 Overlay 所处的分镜
 */
export function findOverlayStoryboard(tracks: Track[], overlay?: Overlay): IndexableOverlay | null {
  if (!overlay || typeof overlay.storyboardIndex !== 'number') {
    return null
  }

  const storyboardTrack = tracks.find(t => t.type === TrackType.STORYBOARD)
  if (!storyboardTrack) {
    return null
  }

  const sorted = storyboardTrack
    .overlays
    .sort(byStartFrame())

  const storyboard = sorted[overlay.storyboardIndex] as StoryboardOverlay

  if (storyboard) {
    return { ...storyboard, index: overlay.storyboardIndex }
  }

  return null
}

/**
 * 根据起始帧和终止帧, 查找处于其间的所有 Overlay
 */
export function findOverlaysBetweenFrames(
  overlays: Overlay[],
  startFrame: number,
  endFrame: number,
  comparisonBasis: 'start' | 'end' | 'center',
  threshold: number = 0
): IndexableOverlay[] {
  threshold = Math.abs(threshold)

  return overlays
    .sort(byStartFrame())
    .map((o, index) => ({ ...o, index }))
    .filter(overlay => {
      const basis = overlay.durationInFrames < 2 * threshold || comparisonBasis === 'center'
        ? overlay.from + overlay.durationInFrames / 2
        :  comparisonBasis === 'start'
          ? overlay.from + threshold
          : overlay.from + overlay.durationInFrames - threshold

      return startFrame < basis && basis < endFrame
    })
}

/**
 * 获取分镜的序号
 */
export function findStoryboardIndex(tracks: Track[], overlay: StoryboardOverlay): number {
  if (overlay.type !== OverlayType.STORYBOARD) return -1

  const storyboardTrack = tracks.find(t => t.type === TrackType.STORYBOARD)
  if (!storyboardTrack) return -1

  return storyboardTrack
    .overlays
    .sort(byStartFrame())
    .findIndex(o => o.id === overlay.id)
}

/**
 * 获取指定位置上的分镜
 */
export function getStoryboardAt(tracks: Track[], index: number): StoryboardOverlay | null {
  return (
    tracks
      .find(t => t.type === TrackType.STORYBOARD)
      ?.overlays[index]
    || null
  ) as StoryboardOverlay | null
}

/**
 * 查找指定分镜下的所有 Overlay
 */
export function findOverlaysAboveStorybook(tracks: Track[], storyboard: StoryboardOverlay): (Overlay & { trackIndex: number })[] {
  const targetStoryboardIndex = findStoryboardIndex(tracks, storyboard)

  if (targetStoryboardIndex === -1) {
    return []
  }

  return tracks
    .filter(t => t.type !== TrackType.STORYBOARD && !t.isGlobalTrack)
    .map(({ overlays }, index) => overlays.map(o => ({ ...o, trackIndex: index })))
    .flat()
    .filter(({ storyboardIndex }) => storyboardIndex === targetStoryboardIndex)
}

/**
 * 计算 Overlay 变速的最小限制值
 * 防止调低变速时与后续 Overlay 发生重叠
 */
export function calculateMinSpeedLimit(
  tracks: Track[],
  targetOverlay: Overlay & Progressive
): number {
  const MIN_SPEED = 0.1

  // 基础验证
  if (!targetOverlay || !tracks.length) {
    return MIN_SPEED
  }

  // 查找目标 Overlay 所在的轨道
  const currentTrack = findTrackByOverlay(tracks, targetOverlay.id)
  if (!currentTrack || currentTrack.isGlobalTrack) {
    return MIN_SPEED
  }

  const storyboard = findOverlayStoryboard(tracks, targetOverlay)

  // 获取当前轨道上的所有 Overlay，按起始帧排序
  const relevantOverlays = currentTrack.overlays
    .filter(forRelevantOverlays({
      storyboard,
      track: currentTrack,
      currentOverlay: targetOverlay
    }))
    .sort(byStartFrame())

  // 查找目标 Overlay 在轨道中的位置
  const targetIndex = relevantOverlays.findIndex(overlay => overlay.id === targetOverlay.id)
  if (targetIndex === -1) {
    return MIN_SPEED
  }

  // 计算可用空间
  const maxDuration = (() => {
    if (!currentTrack.isGlobalTrack && currentTrack.type === TrackType.VIDEO && storyboard) {
      return calculateLeftSpaceOfStoryboard(storyboard, relevantOverlays) + targetOverlay.durationInFrames
    }

    const storyboardEnd = storyboard ? storyboard.from + storyboard.durationInFrames : Infinity
    const nextOverlay = relevantOverlays[targetIndex + 1]
    const maxEndFrame = nextOverlay?.from ?? storyboardEnd
    return maxEndFrame - targetOverlay.from
  })()

  // 如果已经没有可用空间或重叠，返回当前速度作为最小值
  if (maxDuration <= 0) {
    return Math.max(targetOverlay.speed ?? 1, MIN_SPEED)
  }

  // 计算原始时长（不受变速影响的时长）
  const originalDuration = targetOverlay.originalDurationFrames -
    (targetOverlay.trimStartFrames ?? 0) -
    (targetOverlay.trimEndFrames ?? 0)

  // 验证原始时长有效性
  if (originalDuration <= 0) {
    return MIN_SPEED // 原始时长无效，使用默认值
  }

  const minSpeed = originalDuration / maxDuration
  return clamp(minSpeed, MIN_SPEED, targetOverlay.speed ?? 1)
}

/**
 * 根据 overlay 类型映射到对应的资源类型
 */
export function mapOverlayTypeToResourceType(overlayType: OverlayType): ResourceCacheType | null {
  switch (overlayType) {
    case OverlayType.VIDEO:
      return ResourceCacheType.VIDEO
    case OverlayType.SOUND:
      return ResourceCacheType.SOUND
    case OverlayType.STICKER:
      return ResourceCacheType.PASTER
    case OverlayType.TEXT:
      return ResourceCacheType.FONT
    default:
      return null
  }
}

/**
 * 根据原始轨道数据, 生成最终传递给渲染器的 Overlay。
 * @param tracks 原始轨道数据
 * @param activationByType
 * 混剪轨道的激活状态.
 * - Key 为轨道类型.
 * - Value：
 *   - 当 Value 为数组时, 数组的索引为分镜序号, 值为轨道索引.
 *   - 当 Value 为 Map 时, Key 为分镜序号, Value 为轨道索引.
 * @param omitStoryboard 生成时是否去除分镜 Overlay
 * @param useCacheUrl 是否使用本地缓存 URL
 */
export function calculateRenderableOverlays(
  tracks: Track[],
  activationByType: Partial<Record<TrackType, Pick<Map<number, number>, 'get'> | number[]>>,
  omitStoryboard = true,
  useCacheUrl = true
): RenderableOverlay[] {
  const storyboards = getStoryboards(tracks)

  /**
   * 从缓存中获取本地路径并更新 overlay 的 src
   */
  const _updateOverlayWithCachedPath = (overlay: RenderableOverlay): RenderableOverlay => {
    if (!useCacheUrl) {
      return omit(overlay, 'localSrc') as RenderableOverlay
    }

    if (overlay.localSrc) return overlay

    // 检查 overlay 是否有 src 属性
    if (!('src' in overlay) || !overlay.src) {
      return overlay
    }

    if (overlay.type === OverlayType.TEXT) {
      return {
        ...overlay,
        localSrc: cacheManager.font.getFontLocalUrlSync(overlay.src) || undefined
      }
    }

    const primaryResourceType = mapOverlayTypeToResourceType(overlay.type)
    if (!primaryResourceType) {
      return overlay
    }

    try {
      // 首先尝试主要资源类型
      let cachedPath = cacheManager.resource.getResourcePathSync(primaryResourceType, overlay.src)

      // 如果主要类型没有找到，对于视频类型尝试其他可能的资源类型
      if (!cachedPath && overlay.type === OverlayType.VIDEO) {
        // 尝试其他可能的资源类型
        const fallbackTypes = [ResourceCacheType.SOUND, ResourceCacheType.SUFFIXLESS_SOUND]
        for (const fallbackType of fallbackTypes) {
          cachedPath = cacheManager.resource.getResourcePathSync(fallbackType, overlay.src)
          if (cachedPath) {
            break
          }
        }
      }

      if (cachedPath) {
        // 创建新的 overlay 对象，使用本地缓存路径
        return {
          ...overlay,
          localSrc: cachedPath
        } as RenderableOverlay
      }
    } catch (error) {
      console.warn(`获取缓存路径失败 [${overlay.type}]:`, overlay.src, error)
    }

    // 如果没有缓存路径，返回原始 overlay
    return overlay as RenderableOverlay
  }

  const _limitOverlayDuration = (overlay: RenderableOverlay) => {
    const storyboard = storyboards[overlay.storyboardIndex ?? -1]
    if (!storyboard) return overlay
    const [, storyboardEnd] = getOverlayTimeRange(storyboard)
    const [overlayFrom] = getOverlayTimeRange(overlay)
    return {
      ...overlay,
      durationInFrames: Math.min(overlay.durationInFrames, storyboardEnd - overlayFrom)
    }
  }

  const _addZIndexForOverlay = (overlay: Overlay, trackIndex: number) => {
    return merge(overlay, {
      zIndex: overlay.type === OverlayType.VIDEO
        ? 1
        : (tracks.length - trackIndex) * 10
    })
  }

  return tracks
    .map(({ overlays, type: trackType, isGlobalTrack }, trackIndex) => {
      if (omitStoryboard && trackType === TrackType.STORYBOARD) return []

      if (isGlobalTrack) return overlays.map(o => _addZIndexForOverlay(o, trackIndex))

      const activation = activationByType[trackType]
      if (activation) {
        return overlays
          .map((o, index) => ({ ...o, index }))
          .filter(overlay => {
            if (typeof overlay.storyboardIndex !== 'number') return false

            return Array.isArray(activation)
              ? activation[overlay.storyboardIndex] === trackIndex
              : activation.get(overlay.storyboardIndex) === trackIndex
          })
          .map(o => {
            return _addZIndexForOverlay(o, trackIndex)
          })
      }

      return overlays.map(o => _addZIndexForOverlay(o, trackIndex))
    })
    .flat()
    .map(_updateOverlayWithCachedPath)
    .map(_limitOverlayDuration)
}

export function calculateDuration(overlays: Overlay[]) {
  return overlays.reduce((max, overlay) => {
    const endFrame = overlay.from + overlay.durationInFrames
    return Math.max(max, endFrame)
  }, 0)
}

export function createTextOverlay(
  font: opentype.Font,
  overrides: Partial<Omit<TextOverlay, 'styles'>> = {},
  stylesOverrides: Partial<TextOverlay['styles']> = {}
): TextOverlay {
  const base: TextOverlay = {
    ...DEFAULT_TEXT_OVERLAY,
    ...overrides,
    styles: {
      ...DEFAULT_TEXT_OVERLAY.styles,
      ...stylesOverrides
    }
  }

  const { minHeight } = calculateTextRenderInfo(font, base)
  base.height = Math.max(minHeight, base.height)

  return base
}

export function isValidProgressiveOverlay(overlay: Overlay): overlay is Overlay & Progressive {
  return 'originalDurationFrames' in overlay && typeof overlay.originalDurationFrames === 'number'
}

/**
 * 计算 ProgressiveOverlay 的实际播放时长（考虑变速、去片头、去片尾）
 * @param overlay 视频 Overlay
 * @returns 实际播放时长（帧数）
 */
export function calculateProgressiveOverlayDuration(overlay: Overlay & Progressive) {
  const originalDuration = overlay.originalDurationFrames
  const trimStart = overlay.trimStartFrames || 0
  const trimEnd = overlay.trimEndFrames || 0
  const speed = overlay.speed || 1

  // 计算去除片头片尾后的时长
  const trimmedDuration = originalDuration - trimStart - trimEnd

  // 考虑播放速度的影响：实际播放时长 = 原始时长 / 播放速度
  const actualDuration = trimmedDuration / speed

  return Math.max(0, actualDuration)
}

export function updateProgressiveOverlayDuration(overlay: Overlay & Progressive) {
  return {
    ...overlay,
    durationInFrames: calculateProgressiveOverlayDuration(overlay)
  }
}
