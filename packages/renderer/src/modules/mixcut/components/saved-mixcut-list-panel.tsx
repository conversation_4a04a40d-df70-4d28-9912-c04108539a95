import React from 'react'
import { Mixcut } from '@/types/mixcut'
import { useVirtualTab } from '@/contexts'
import { MultiSelectableCard } from './multi-selectable-card'
import { clsx } from 'clsx'
import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { EditorModule } from '@/libs/request/api/editor'
import { useMixcutContext } from '../context/context'
import { AuthedImg } from '@/components/authed-img'
import { formatRepetitionRate } from '@app/shared/utils'
import { cacheManager } from '@/libs/cache/cache-manager'

const SavedMixcutCard: React.FC<Mixcut.SavedMixcut & { index: number }> = ({ index: i, ...mixcut }) => {
  const { saved } = useMixcutContext()

  return (
    <MultiSelectableCard {...saved} index={i}>
      <div
        className={clsx(
          'w-48 h-64 relative outline-3 cursor-pointer rounded',
        )}
      >
        {/* 预览图片背景 */}
        <AuthedImg
          src={mixcut.cover}
          alt={`saved-mixcut-${mixcut.id}`}
          className="w-full h-full object-cover rounded"
          onError={e => {
            // 图片加载失败时显示默认背景
            e.currentTarget.style.display = 'none'
          }}
        />

        {/* 重复率标签 */}
        <div className="absolute right-1 top-1 bg-black/70 text-white p-1 text-xs rounded">
          重复率{formatRepetitionRate(mixcut.repetitionRate)}
        </div>
      </div>
    </MultiSelectableCard>
  )
}

export const SavedMixcutListPanel = () => {
  const { params } = useVirtualTab('Mixcut')
  const { scriptId } = params
  const { data = [] } = useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: async () => {
      const { list } = await EditorModule.listMixcuts(scriptId)

      // 异步缓存混剪数据，不阻塞主流程
      cacheManager.mixcut.cacheMixcut(...list).catch(error => {
        console.error('缓存混剪数据失败:', error)
      })

      return list
    }
  })

  return (
    <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
      {data
        ?.sort((a, b) => a.repetitionRate - b.repetitionRate)
        .map((combo, i) => (
          <SavedMixcutCard
            {...combo}
            key={i}
            index={i}
          />
        ))}
    </div>
  )
}
