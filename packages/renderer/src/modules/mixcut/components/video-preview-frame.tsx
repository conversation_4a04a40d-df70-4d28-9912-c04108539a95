import React, { useMemo } from 'react'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import { useLoadTileImageInfo } from '@/hooks/material/useLoadTileImageInfo'
import { TileRenderer } from '@/components/material/TileRenderer'

export const VideoPreviewFrame: React.FC<{ overlay?: VideoOverlay }> = ({ overlay }) => {
  const fallback = (
    <div className="w-full h-full bg-gray-500 flex items-center justify-center">
      <div className="text-white/60 text-sm">无预览</div>
    </div>
  )

  if (!overlay?.originalMeta.tileUrl) return fallback

  const { width, height } = overlay.originalMeta

  const tileInfo = useLoadTileImageInfo(overlay.originalMeta.tileUrl)

  if (!tileInfo) {
    return fallback
  }

  const currentFrame = useMemo(() => {
    const { trimStartFrames = 0, originalDurationFrames } = overlay

    return Math.floor(trimStartFrames / originalDurationFrames * tileInfo.totalFrames)
  }, [overlay, tileInfo])

  return (
    <div className="w-full h-full bg-gray-500 flex items-center justify-center relative overflow-hidden rounded-sm">
      <TileRenderer tileInfo={tileInfo} currentFrame={currentFrame} aspectRatio={width / height} />
    </div>
  )
}
