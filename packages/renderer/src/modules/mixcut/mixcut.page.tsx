import React, { useEffect, useMemo, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { MoreHorizontal } from 'lucide-react'
import { useVirtualTab } from '@/contexts'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { useQuery } from '@tanstack/react-query'
import { Player, PlayerRef } from '@remotion/player'
import { Renderer } from '@clipnest/overlay-renderer'
import { OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'
import { cacheManager } from '@/libs/cache/cache-manager'
import { LoadingIndicator } from '@/components/LoadingIndicator'

import { VideoPreviewFrame } from './components/video-preview-frame'
import { GeneratedMixcutListPanel } from './components/generated-mixcut-list-panel'
import { Toolbar } from './components/toolbar'
import { SavedMixcutListPanel } from './components/saved-mixcut-list-panel'
import { useMixcutContext } from './context/context'
import { MixcutProvider } from './context/provider'
import { BatchUploadOverlay } from './components/BatchUploadOverlay'
import { BrandTabs } from '@/components/ui/brand-tabs'

// 顶部 - 标签栏组件
const PreviewTabBar = () => {
  const { activeTab, setActiveTab } = useMixcutContext()

  return (
    <BrandTabs
      activeTab={activeTab}
      onTabChange={setActiveTab}
      tabs={[
        { value: 'generation', label: '生成混剪',  },
        { value: 'saved', label: '我保存的混剪' },
      ]}
    />
  )
}

// 中右 - 混剪结果分镜预览
const StoryboardPanel: React.FC = () => {
  const { state, playerOverlays, generation: { activeItem } } = useMixcutContext()

  const items = useMemo(() => {
    return playerOverlays
      .filter(o => o.type === OverlayType.VIDEO && typeof o.storyboardIndex === 'number')
      .sort((a, b) => a.storyboardIndex! - b.storyboardIndex!)
  }, [playerOverlays])

  if (!items.length || !activeItem) return null

  return (
    <div className="border-l border-border px-4 pt-8 flex flex-col">
      <div className="pb-3">分镜视频预览</div>
      <div className="flex-1 flex flex-col gap-3 overflow-y-auto pr-2">
        {items.map(item => (
          <div key={item.id} >
            <div
              style={{
                aspectRatio: state.playerMetadata.width / state.playerMetadata.height
              }}
              className="w-32 rounded-sm"
            >
              <VideoPreviewFrame overlay={item as VideoOverlay} />
            </div>
            <div className="text-sm text-gray-400 mt-1.5">
              分镜{item.storyboardIndex! + 1} - 轨道{activeItem.videoCombo.selections[item.storyboardIndex!]}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// 右侧 - 视频预览面板组件
const VideoPreviewPanel = () => {
  const { playerOverlays, state: { playerMetadata } } = useMixcutContext()

  const playerRef = useRef<PlayerRef | null>(null)

  const { width, height, fps, durationInFrames } = playerMetadata

  useEffect(() => {
    const player = playerRef.current

    if (player) {
      player.seekTo(0)
      player.play()
    }
  }, [playerOverlays])

  return (
    <div className="w-[30vw] border-l border-border bg-background flex flex-col">
      {/* 预览标题 */}
      <div className="p-4 border-b border-border">
        <h3 className="text-sm font-medium text-foreground flex items-center">
          预览窗口
          <Button variant="ghost" size="sm" className="ml-auto h-6 w-6 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </h3>
      </div>

      {/* 视频播放器区域 */}
      <div className="flex-1 bg-black relative px-4">
        {
          playerOverlays.length
            ? (
              <Player
                ref={playerRef}
                controls
                loop={false}
                component={Renderer}
                compositionWidth={width}
                compositionHeight={height}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                fps={fps}
                durationInFrames={durationInFrames}
                inputProps={{
                  overlays: playerOverlays,
                  playerMetadata
                } as any}
              />
            )
            : (
              // 视频播放器占位
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-white/60 text-sm text-center">在左侧选择一个结果后 <br />可以在此处预览完整视频</div>
              </div>
            )
        }
      </div>
    </div>
  )
}

// 主内容区域组件
const PreviewMainContent = () => {
  const { activeTab } = useMixcutContext()

  return (
    <div className="flex flex-1 overflow-hidden">
      {activeTab === 'generation' ? <GeneratedMixcutListPanel /> : <SavedMixcutListPanel />}
      <StoryboardPanel />
      <VideoPreviewPanel />
    </div>
  )
}

export default function MixcutPage() {
  const { params, id } = useVirtualTab('Mixcut')
  const scriptId = params.scriptId
  const { closeTab } = useVirtualTabsStore()

  const { data: state = null, isLoading } = useQuery({
    queryKey: ['PROJECT_STATE', scriptId],
    queryFn: () => cacheManager.projectState.loadProjectState(scriptId, true),
  })

  if (!scriptId || !state) {
    if (isLoading) return <LoadingIndicator />

    closeTab(id)
    return null
  }

  return (
    <MixcutProvider state={state} defaultTab={params.defaultTab}>
      <div className="flex flex-col h-full bg-background p-4">
        <PreviewTabBar />
        <Toolbar />
        <PreviewMainContent />
      </div>

      {/* 批量上传进度遮罩 */}
      <BatchUploadOverlay />
    </MixcutProvider>
  )
}
