export type Combo = {
  selections: number[]
  similarity: number
  max?: number
}

export type PlayerMetadata = {
  width: number
  height: number
  fps: number
  durationInFrames: number
}

export type RenderRequestPayload = {
  id: string
  inputProps: {
    overlays: any[],
    playerMetadata: PlayerMetadata
  }
}

/**
 * 混剪缓存数据项
 */
export type MixcutCacheItem = {
  id: number
  url: string
  encryptedData: string
  cachedAt: number
  originalUrl: string
}

/**
 * 混剪缓存请求参数
 */
export type CacheMixcutParams = {
  mixcuts: Array<{
    id: number
    url: string
  }>
}

/**
 * 混剪缓存响应结果
 */
export type CacheMixcutResult = {
  success: boolean
  cached: MixcutCacheItem[]
  errors: Array<{
    id: number
    error: string
  }>
}

/**
 * 混剪IPC客户端接口
 */
export interface MixcutIPCClient {

  /**
   * 生成混剪组合
   * @param props.limit 生成数量上限
   * @param props.threshold 生成结果的相似度阈值
   * @param props.matrix 每一列中可用的行序号
   */
  generateCombos(props: { limit: number, threshold: number, matrix: number[][] }): Promise<Combo[]>

  /**
   * 上传混剪结果到服务器
   * @param params 上传参数
   * @returns Promise<any> 上传结果
   */
  uploadMixcutResult(params: {
    scriptId: string
    data: RenderRequestPayload
    similarity: number
    cover?: string
    duration?: number
  }): Promise<any>

  /**
   * 缓存混剪数据到本地
   * @param params 缓存参数
   * @returns Promise<CacheMixcutResult> 缓存结果
   */
  cacheMixcutData(params: CacheMixcutParams): Promise<CacheMixcutResult>

  /**
   * 获取单个混剪的解密数据
   * @param params.id 混剪 ID
   * @param params.encryptedData 加密数据
   * @returns Promise<any> 解密后的数据
   */
  decryptMixcutData(params: { id: number, encryptedData: string }): Promise<any>
}
