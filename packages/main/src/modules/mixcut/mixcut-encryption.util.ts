import crypto from 'crypto'
import zlib from 'zlib'
import { promisify } from 'util'

const gzip = promisify(zlib.gzip)
const gunzip = promisify(zlib.gunzip)

/**
 * 主进程混剪数据加密工具
 * 使用 AES-256-GCM 加密算法对 JSON 数据进行加密和解密
 * 使用 gzip 压缩以减少存储体积
 */
export class MixcutEncryption {
  
  // 硬编码的加密密钥（根据需求暂时采用明文存储）
  private static readonly ENCRYPTION_KEY = 'mixcut-cache-key-2024-main-process'
  private static readonly ALGORITHM = 'aes-256-gcm'

  /**
   * 生成密钥的 SHA-256 哈希
   */
  private static getKeyHash(): Buffer {
    return crypto.createHash('sha256').update(this.ENCRYPTION_KEY).digest()
  }

  /**
   * 压缩并加密 JSON 数据
   * @param data 要加密的数据对象
   * @returns 压缩加密后的 Base64 字符串
   */
  static async compressAndEncrypt(data: any): Promise<string> {
    try {
      // 1. 序列化为 JSON
      const jsonString = JSON.stringify(data)

      // 2. 使用 gzip 压缩
      const compressed = await gzip(Buffer.from(jsonString, 'utf8'))

      // 3. 生成随机 IV
      const iv = crypto.randomBytes(16)

      // 4. 创建加密器
      const cipher = crypto.createCipherGCM(this.ALGORITHM, this.getKeyHash(), iv)
      cipher.setAAD(Buffer.from('mixcut-data'))

      // 5. 加密压缩数据
      const encrypted = Buffer.concat([
        cipher.update(compressed),
        cipher.final()
      ])

      // 6. 获取认证标签
      const authTag = cipher.getAuthTag()

      // 7. 组合 IV + 认证标签 + 加密数据
      const result = Buffer.concat([iv, authTag, encrypted])

      // 8. 转换为 Base64
      return result.toString('base64')
    } catch (error) {
      console.error('[MixcutEncryption] 压缩加密失败:', error)
      throw new Error('数据压缩加密失败')
    }
  }

  /**
   * 解密并解压数据
   * @param encryptedData 压缩加密的 Base64 字符串
   * @returns 解密解压后的数据对象
   */
  static async decryptAndDecompress<T = any>(encryptedData: string): Promise<T> {
    try {
      // 1. 从 Base64 解码
      const buffer = Buffer.from(encryptedData, 'base64')

      // 2. 提取 IV、认证标签和加密数据
      const iv = buffer.subarray(0, 16)
      const authTag = buffer.subarray(16, 32)
      const encrypted = buffer.subarray(32)

      // 3. 创建解密器
      const decipher = crypto.createDecipherGCM(this.ALGORITHM, this.getKeyHash(), iv)
      decipher.setAAD(Buffer.from('mixcut-data'))
      decipher.setAuthTag(authTag)

      // 4. 解密数据
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ])

      // 5. 解压数据
      const decompressed = await gunzip(decrypted)

      // 6. 解析 JSON
      const jsonString = decompressed.toString('utf8')
      return JSON.parse(jsonString)
    } catch (error) {
      console.error('[MixcutEncryption] 解密解压失败:', error)
      throw new Error('数据解密解压失败')
    }
  }

  /**
   * 简单加密（不压缩，用于较小的数据）
   * @param data 要加密的数据对象
   * @returns 加密后的 Base64 字符串
   */
  static encrypt(data: any): string {
    try {
      const jsonString = JSON.stringify(data)
      const iv = crypto.randomBytes(16)
      const cipher = crypto.createCipherGCM(this.ALGORITHM, this.getKeyHash(), iv)

      const encrypted = Buffer.concat([
        cipher.update(Buffer.from(jsonString, 'utf8')),
        cipher.final()
      ])

      const authTag = cipher.getAuthTag()
      const result = Buffer.concat([iv, authTag, encrypted])

      return result.toString('base64')
    } catch (error) {
      console.error('[MixcutEncryption] 加密失败:', error)
      throw new Error('数据加密失败')
    }
  }

  /**
   * 简单解密（不解压）
   * @param encryptedData 加密的 Base64 字符串
   * @returns 解密后的数据对象
   */
  static decrypt<T = any>(encryptedData: string): T {
    try {
      const buffer = Buffer.from(encryptedData, 'base64')
      const iv = buffer.subarray(0, 16)
      const authTag = buffer.subarray(16, 32)
      const encrypted = buffer.subarray(32)

      const decipher = crypto.createDecipherGCM(this.ALGORITHM, this.getKeyHash(), iv)
      decipher.setAuthTag(authTag)

      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ])

      const jsonString = decrypted.toString('utf8')
      return JSON.parse(jsonString)
    } catch (error) {
      console.error('[MixcutEncryption] 解密失败:', error)
      throw new Error('数据解密失败')
    }
  }
}
